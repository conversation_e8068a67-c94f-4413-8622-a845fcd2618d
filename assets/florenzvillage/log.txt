INFO(chunker.cpp:38): Start chunking... 
INFO(chunker.cpp:794): Environment Data create finished , free file stream... 
INFO(chunker.cpp:798): Environment Data create finished , free file stream success.
INFO(chunker.cpp:353): chunking finished . 
INFO(indexer.cpp:29): Start Index ,need a little time ... 
INFO(indexer.cpp:38): Index finished ,Start create Data... 
INFO(indexer.cpp:929): Data create finished , free file stream... 
INFO(indexer.cpp:934): Data create finished , free file stream success.
INFO(indexer.cpp:40): Index finished ,Start create Index... 
INFO(indexer.cpp:42): Index finished ,Start create meta data 
INFO(indexer.cpp:45): All tasks finished .
INFO(meshSplit.cpp:388): parsing 144.005mb in 1.31742 seconds [109.308 MBps]
INFO(meshSplit.cpp:391): Read 4185791 total vertices 
INFO(meshSplit.cpp:393): Read 7213472 total faces (triangles) 
INFO(meshSplit.cpp:412): xyzOrder is 012
INFO(meshSplit.cpp:437): face list count: 3
INFO(meshSplit.cpp:551): grid size: 32 x 39
INFO(meshSplit.cpp:667): split done ...
INFO(meshSplit.cpp:958): dump mesh num: 244
INFO(meshSplit.cpp:1009): save done...
INFO(meshSplit.cpp:1156): meshSplit success !!!
